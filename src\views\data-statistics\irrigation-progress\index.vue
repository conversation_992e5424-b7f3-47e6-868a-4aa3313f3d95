<template>
  <div class="common-table-page">
    <!-- 筛选栏 -->
    <VxeTableForm @handleQuery="handleQuery" @resetQuery="resetQuery">
      <a-form-item label="灌溉时间">
        <a-range-picker
          :allowClear="false"
          :value="takeEffect"
          format="YYYY-MM-DD"
          formatValue="YYYY-MM-DD"
          :disabled-date="disabledDate"
          :placeholder="['开始时间', '结束时间']"
          @change="onRangeChange"
        />
      </a-form-item>

      <template #table>
        <VxeTable
          ref="vxeTableRef"
          :border="true"
          :columns="columns"
          :height="710"
          :tableData="tableData"
          :tableTitle="tableTitle"
          :loading="loading"
          :isAdaptPageSize="false"
          :tablePage="false"
          :show-footer="true"
          :footer-data="footerData"
          :merge-footer-items="mergeFooterItems"
        >
          <div class="table-operations" slot="button">
            <a-button type="primary" @click="handleImport()" icon="download" :loading="exportLoading">导入</a-button>
            <a-button type="primary" @click="handleExport()" icon="download" :loading="exportLoading">导出</a-button>
          </div>
        </VxeTable>
      </template>
    </VxeTableForm>
  </div>
</template>

<script lang="jsx">
  import VxeTable from '@/components/VxeTable'
  import VxeTableForm from '@/components/VxeTableForm'
  import { getOptions, getTreeByLoginOrgId } from '@/api/common.js'
  import { getRoundByDate, getIrrigationRound, getIrrigationProgress } from './services.js'
  import moment from 'moment'

  function flattenTree(tree, result = []) {
    for (let node of tree) {
      result.push(node)
      if (node.children) {
        flattenTree(node.children, result)
      }
    }
    return result
  }

  export default {
    name: 'IrrigationProgress',
    components: {
      VxeTableForm,
      VxeTable,
    },
    data() {
      return {
        loading: false,
        exportLoading: false,

        deptOptions: [],
        takeEffect: [moment().format('YYYY-MM-DD'), moment().format('YYYY-MM-DD')],
        defaultDate: [],
        tableTitle: '灌溉进度表',
        irrigationList: [],
        currentId: undefined,
        currenYear: '',
        currentMonth: '',
        irrigationName: '',
        cropOptions: [],
        cropCodeNames: [],
        mergeFooterItems: [{ row: 0, col: 1, rowspan: 1, colspan: 13 }],

        columns: [],
        tableData: [],
        footerData: [],
      }
    },

    watch: {},
    created() {
      getTreeByLoginOrgId().then(treeRes => {
        this.deptOptions = flattenTree(treeRes?.data)
      })
      getOptions('crop').then(res => {
        this.cropOptions = res?.data
      })

      this.getRound(moment().format('YYYY-MM-DD'))
    },
    methods: {
      // 禁用今天以后的日期
      disabledDate(current) {
        // 禁用今天以后的日期
        return current && current > moment().endOf('day')
      },
      onRangeChange(value, dateString) {
        this.takeEffect = value
        if (dateString?.length == 0) {
          return
        }
        this.getRound(moment(dateString[1]).format('YYYY-MM-DD'))
      },
      getRound(endDate) {
        this.currentMonth = moment(endDate).format('MM月DD日')
        this.tableData = []
        this.footerData = []
        getRoundByDate({ date: endDate }).then(res => {
          if (res?.data == null) {
            this.$message.warning(`没有该日期所属时间段`, 3)
            return
          }
          this.defaultDate = [moment(res?.data?.startDate), moment(res?.data?.endDate)]
          this.takeEffect = [moment(res?.data?.startDate), moment(res?.data?.endDate)]
          getIrrigationRound({
            startDate: moment(res?.data?.startDate).format('YYYY-MM-DD'),
            endDate: moment(res?.data?.endDate).format('YYYY-MM-DD'),
          }).then(cropRes => {
            this.cropCodeNames = cropRes?.data?.cropCodeNames
            this.currentId = res?.data?.id
            this.irrigationName =
              cropRes?.data?.irrigationRound == 1
                ? '春夏灌'
                : cropRes?.data?.irrigationRound == 2
                  ? '秋灌'
                  : cropRes?.data?.irrigationRound == 3
                    ? '冬灌'
                    : ''
            this.getList()
          })
        })
      },

      /** 查询列表 */
      getList() {
        this.loading = true
        this.tableData = []
        getIrrigationProgress({ id: this.currentId }).then(response => {
          this.currenYear = response?.data?.curYear
          this.tableTitle =
            this.currenYear + '年（' + this.irrigationName + '）灌溉进度表（截止' + this.currentMonth + '）'

          this.irrigationList = response?.data?.statsList
          // this.footerData[0].preYearWaterAmount = response?.data?.remark
          this.footerData = [
            {
              depName: '备注',
              preYearWaterAmount: response?.data?.remark,
            },
          ]
          this.getTableColumns()
          this.getTableData()

          this.loading = false
        })
      },
      getTableColumns() {
        this.columns = [
          { field: 'depName', title: '单位项目', minWidth: 80 },
          {
            title: '干口实引水量（流量日）',
            minWidth: 100,
            field: 'group1',
            headerAlign: 'center',
            children: [
              { field: 'preYearWaterAmount', title: this.currenYear - 1 + '年', minWidth: 58 },
              { field: 'waterAmount', title: this.currenYear + '年', minWidth: 58 },
              {
                field: 'age',
                title: '比' + (this.currenYear - 1) + '年（+,-）',
                minWidth: 110,
                slots: {
                  default: ({ row, rowIndex }) => {
                    return (
                      <div>
                        {this.irrigationList[rowIndex]?.waterAmount - this.irrigationList[rowIndex]?.preYearWaterAmount}
                      </div>
                    )
                  },
                },
              },
            ],
          },

          {
            title: '浇地面积（万亩）',
            field: 'group',
            headerAlign: 'center',
            children: [
              {
                field: 'group1',
                title: this.currenYear + '年',
                headerAlign: 'center',

                children: [
                  { field: 'dryLandPourAmount', title: '干地', minWidth: 60 },
                  { field: 'hotWaterLandPourAmount', title: '热水地', minWidth: 60 },

                  ...[...this.cropCodeNames].map((item, index) => {
                    return {
                      title: item,
                      minWidth: 70,
                      slots: {
                        default: ({ row, rowIndex }) => {
                          return (
                            <div>
                              {this.irrigationList[rowIndex]?.cropPourDetails.find(
                                el => el.code == this.cropOptions.find(el => el.value == item)?.key,
                              )?.pourAmount || ''}
                            </div>
                          )
                        },
                      },
                    }
                  }),
                  {
                    title: '合计',
                    minWidth: 80,
                    slots: {
                      default: ({ row, rowIndex }) => {
                        return (
                          <div>
                            {this.irrigationList[rowIndex]?.dryLandPourAmount +
                              this.irrigationList[rowIndex]?.hotWaterLandPourAmount +
                              this.irrigationList[rowIndex]?.cropPourDetails.reduce((accumulator, currentValue) => {
                                return accumulator + currentValue.pourAmount
                              }, 0)}
                          </div>
                        )
                      },
                    },
                  },
                ],
              },
              {
                title: '比' + (this.currenYear - 1) + '年（+,-）',
                minWidth: 110,
                slots: {
                  default: ({ row, rowIndex }) => {
                    return (
                      <div>
                        {this.irrigationList[rowIndex]?.dryLandPourAmount +
                          this.irrigationList[rowIndex]?.hotWaterLandPourAmount +
                          this.irrigationList[rowIndex]?.cropPourDetails.reduce((accumulator, currentValue) => {
                            return accumulator + currentValue.pourAmount
                          }, 0) -
                          this.irrigationList[rowIndex]?.preYearPourAmount}
                      </div>
                    )
                  },
                },
              },
            ],
          },

          { field: 'pourEfficiency', title: this.currenYear + '年浇地效率', minWidth: 60 },
          { field: 'irrigationQuota', title: '灌溉定额', minWidth: 60 },
        ]
      },
      getTableData() {
        this.tableData = []
        this.irrigationList.forEach(el => {
          this.tableData.push({
            ...el,
            depName:
              el.depId == 0
                ? '报总局'
                : el.depId == -1
                  ? '全灌域'
                  : this.deptOptions.find(item => item.deptId == el.depId)?.deptName,
          })
        })
        this.$nextTick(() => {
          this.mergeFooterItems = [{ row: 0, col: 1, rowspan: 1, colspan: 13 }]
        })
      },
      /** 搜索按钮操作 */
      handleQuery() {
        this.getList()
      },
      /** 重置按钮操作 */
      resetQuery() {
        this.getRound(moment().format('YYYY-MM-DD'))
        this.handleQuery()
      },

      handleImport() {},
      handleExport() {},
    },
  }
</script>
<style lang="less" scoped>
  .table-operations {
    .ant-btn {
      &:last-child {
        margin-right: 0px;
      }
    }
  }
  ::v-deep(.header-bar) {
    .title {
      text-align: center;
      width: 100%;
      position: absolute;
    }
  }
</style>
