<template>
  <div class="common-table-page">
    <!-- 筛选栏 -->
    <VxeTableForm @handleQuery="handleQuery" @resetQuery="resetQuery">
      <div class="flex row-pad">
        <a-form-item label="年份">
          <a-date-picker
            :allowClear="false"
            mode="year"
            format="YYYY"
            v-model="yearValue"
            placeholder="请选择"
            :open="yearShowOne"
            style="width: 120px"
            @keyup.enter.native="handleQuery"
            @openChange="openChangeOne"
            @panelChange="panelChangeOne"
          ></a-date-picker>
        </a-form-item>
        <a-form-item label="单位" style="margin-left: 20px">
          <a-select
            style="width: 130px"
            v-model="queryParam.depId"
            placeholder="请选择"
            :disabled="loginOrgId != 10020"
            @change="changeDept"
          >
            <a-select-option v-for="(d, index) in deptOptions" :key="index" :value="d.deptId">
              {{ d.deptName }}
            </a-select-option>
          </a-select>
        </a-form-item>
      </div>

      <template #table>
        <VxeTable
          ref="vxeTableRef"
          :border="true"
          :columns="columns"
          :tableData="tableData"
          :tableTitle="tableTitle"
          :loading="loading"
          :isAdaptPageSize="false"
          :tablePage="false"
          :show-footer="false"
        >
          <div class="table-operations" slot="button">
            <a-button type="primary" @click="handleImport()" icon="download" :loading="exportLoading">导入</a-button>
            <a-button type="primary" @click="handleExport()" icon="download" :loading="exportLoading">导出</a-button>
          </div>
        </VxeTable>
      </template>
    </VxeTableForm>
  </div>
</template>

<script lang="jsx">
  import VxeTable from '@/components/VxeTable'
  import VxeTableForm from '@/components/VxeTableForm'
  import { getTreeByLoginOrgId } from '@/api/common.js'
  import moment from 'moment'
  import { getDeptList, getWaterQuantityFeeList, getQuantityFeeStatistics, getIrrigationDate } from './services.js'

  import { getMonthsBetweenDates } from '@/utils/aidex'
  import axios from 'axios'
  import storage from 'store'
  import { ACCESS_TOKEN } from '@/store/mutation-types'

  export default {
    name: 'WaterRateWaterRent',
    components: {
      VxeTableForm,
      VxeTable,
    },
    data() {
      return {
        loading: false,
        yearShowOne: false,
        tableTitle: '年公管渠水量、水费统计表',

        deptOptions: [],
        exportLoading: false,
        loginOrgId: JSON.parse(localStorage.getItem('user'))?.loginOrgId,
        queryParam: {
          depId: undefined,
          fillYear: moment().year(),
        },

        yearValue: moment(),
        irrigationStage: [],
        columns: [],
        tableData: [],
      }
    },
    computed: {},
    watch: {},

    created() {
      getDeptList({ parentId: 10020 }).then(res => {
        this.deptOptions = this.deptOptions.concat(res?.data)
        this.queryParam.depId = this.deptOptions[0]?.deptId
      })

      getWaterQuantityFeeList({
        depId: this.queryParam.depId,
        pageNum: 1,
        pageSize: 1,
        sort: [{ direction: 'desc', property: 'fillYear' }],
      }).then(res => {
        if (res?.data?.data?.length) {
          this.queryParam.fillYear = moment(res?.data?.data[0]?.fillTime + '').format('YYYY')
          this.yearValue = moment(res?.data?.data[0]?.fillTime + '')
        }
        this.getMonths()
      })
    },
    methods: {
      getMonths() {
        Promise.allSettled([
          getIrrigationDate({ fillYear: this.queryParam.fillYear, irrigationRound: 1 }),
          getIrrigationDate({ fillYear: this.queryParam.fillYear, irrigationRound: 2 }),
          getIrrigationDate({ fillYear: this.queryParam.fillYear, irrigationRound: 3 }),
        ]).then(results => {
          const res1 = results[0]?.value?.data
          const res2 = results[1]?.value?.data
          const res3 = results[2]?.value?.data
          this.irrigationStage = []
          this.irrigationStage.push(
            {
              months: moment(res1?.ssStart).format('M月') + '-' + moment(res1?.ssEnd).format('M月'),
              monthsList: getMonthsBetweenDates(res1?.ssStart, res1?.ssEnd),
            },
            {
              months: moment(res2?.ssStart).format('M月') + '-' + moment(res2?.ssEnd).format('M月'),
              monthsList: getMonthsBetweenDates(res2?.ssStart, res2?.ssEnd),
            },
            {
              months: moment(res3?.ssStart).format('M月') + '-' + moment(res3?.ssEnd).format('M月'),
              monthsList: getMonthsBetweenDates(res3?.ssStart, res3?.ssEnd),
            },
          )
          this.getList()
        })
      },
      changeDept() {
        this.getList()
      },
      // 弹出日历和关闭日历的回调
      openChangeOne(status) {
        this.yearShowOne = status
      },

      // 得到年份选择器的值
      panelChangeOne(value) {
        this.yearValue = value
        this.queryParam.fillYear = moment(value).format('YYYY')
        this.yearShowOne = false

        this.getMonths()
      },

      /** 查询列表 */
      getList() {
        this.loading = true
        this.tableData = []
        axios
          .post(process.env.VUE_APP_BASE_API + '/custom/waterQuantityFee/statistics', this.queryParam, {
            headers: { token: storage.get(ACCESS_TOKEN) },
          })
          // getQuantityFeeStatistics(this.queryParam)
          .then(response => {
            if (response?.data?.code == 200) {
              this.tableData = response?.data?.detailVOList
              if (response?.data?.data?.parentChannelName || response?.data?.data?.fillYear) {
                this.tableTitle =
                  response?.data?.data?.parentChannelName + response?.data?.data?.fillYear + '年公管渠水量、水费统计表'
              }
              this.getTableData(response?.data?.data?.detailVOList)
            } else {
              this.$message.warning(response?.data?.message, 2)
            }
            this.loading = false
          })
      },

      getTableColumns() {
        this.columns = [
          {
            title: '',
            field: 'channelName',
            align: 'center',
            width: 110,
            fixed: 'left',
            slots: {
              header: ({ column }) => {
                return (
                  <div class='first-col'>
                    <div class='first-col-top'>项目</div>
                    <div class='first-col-bottom'>直口渠名</div>
                  </div>
                )
              },
              default: ({ row }) => {
                return <div class={row.channelName == '小计' ? 'subtotalColor' : ''}>{row.channelName}</div>
              },
            },
          },
          {
            title: '灌溉面积(亩)',
            minWidth: 80,
            slots: {
              default: ({ row }) => {
                return <div class={row.channelName == '小计' ? 'subtotalColor' : ''}>{row.irrArea}</div>
              },
            },
          },
          {
            title: '级别',
            field: 'sex',
            minWidth: 60,
            slots: {
              default: ({ row }) => {
                return (
                  <div>
                    {row.channelType == 1
                      ? '支' + row.channelTypeSn
                      : row.channelType == 2
                        ? '斗' + row.channelTypeSn
                        : row.channelType == 3
                          ? '农' + row.channelTypeSn
                          : row.channelType == 4
                            ? '毛' + row.channelTypeSn
                            : ''}
                  </div>
                )
              },
            },
          },
          {
            title: '折算率',
            minWidth: 50,
            slots: {
              default: ({ row }) => {
                return <div class={row.channelName == '小计' ? 'subtotalColor' : ''}>{row.conversionRate}</div>
              },
            },
          },
          {
            title: '超水20%内的水资源费价格',
            minWidth: 100,
            slots: {
              default: ({ row }) => {
                return <div class={row.channelName == '小计' ? 'subtotalColor' : ''}>{row.feeLeTwenty}</div>
              },
            },
          },
          {
            title: '超水20%-50%的水资源费价格',
            minWidth: 100,
            slots: {
              default: ({ row }) => {
                return <div class={row.channelName == '小计' ? 'subtotalColor' : ''}>{row.feeGeTwenty}</div>
              },
            },
          },

          ...[...this.irrigationStage].map((el, stageIndex) => {
            return {
              title: el.months,
              field: 'group' + stageIndex,
              headerAlign: 'center',
              children: [
                {
                  title: '包干水量',
                  minWidth: 60,
                  slots: {
                    default: ({ row, rowIndex }) => {
                      return (
                        <div class={row.channelName == '小计' ? 'subtotalColor' : ''}>
                          {stageIndex == 0
                            ? row.waterRateList0?.contractedVolume
                            : stageIndex == 1
                              ? row.waterRateList1?.contractedVolume
                              : stageIndex == 2
                                ? row.waterRateList2?.contractedVolume
                                : ''}
                        </div>
                      )
                    },
                  },
                },
                {
                  field: 'group2' + stageIndex,
                  title: '实用水量',
                  headerAlign: 'center',

                  children: [
                    ...[...this.irrigationStage[stageIndex].monthsList].map((item, index) => {
                      return {
                        title: item,
                        minWidth: 60,
                        slots: {
                          default: ({ row, rowIndex }) => {
                            let practicalWater = ''
                            if (stageIndex == 0) {
                              practicalWater = row?.actualUsageDetailList0?.find(
                                m => m.useMonth == item.replace('月', ''),
                              )?.actualUsage
                            } else if (stageIndex == 1) {
                              practicalWater = row?.actualUsageDetailList1?.find(
                                m => m.useMonth == item.replace('月', ''),
                              )?.actualUsage
                            } else if (stageIndex == 2) {
                              practicalWater = row?.actualUsageDetailList2?.find(
                                m => m.useMonth == item.replace('月', ''),
                              )?.actualUsage
                            }
                            return <div class={row.channelName == '小计' ? 'subtotalColor' : ''}>{practicalWater}</div>
                          },
                        },
                      }
                    }),
                    {
                      title: '合计水量',
                      minWidth: 60,
                      slots: {
                        default: ({ row, rowIndex }) => {
                          let waterSum = null
                          if (stageIndex == 0 && row?.actualUsageDetailList0?.length > 0) {
                            waterSum = row?.actualUsageDetailList0?.reduce((accumulator, currentValue) => {
                              return accumulator + currentValue?.actualUsage
                            }, 0)
                          } else if (stageIndex == 1 && row?.actualUsageDetailList1?.length > 0) {
                            waterSum = row?.actualUsageDetailList1?.reduce((accumulator, currentValue) => {
                              return accumulator + currentValue?.actualUsage
                            }, 0)
                          } else if (stageIndex == 2 && row?.actualUsageDetailList2?.length > 0) {
                            waterSum = row?.actualUsageDetailList2?.reduce((accumulator, currentValue) => {
                              return accumulator + currentValue?.actualUsage
                            }, 0)
                          }
                          return <div class={row.channelName == '小计' ? 'subtotalColor' : ''}>{waterSum}</div>
                        },
                      },
                    },
                  ],
                },

                {
                  field: 'group3' + stageIndex,
                  title: '其中',
                  children: [
                    {
                      title: '指标内水量',
                      minWidth: 90,
                      slots: {
                        default: ({ row, rowIndex }) => {
                          return (
                            <div class={row.channelName == '小计' ? 'subtotalColor' : ''}>
                              {stageIndex == 0
                                ? row.waterRateList0?.allocatedVolume
                                : stageIndex == 1
                                  ? row.waterRateList1?.allocatedVolume
                                  : stageIndex == 2
                                    ? row.waterRateList2?.allocatedVolume
                                    : ''}
                            </div>
                          )
                        },
                      },
                    },
                    {
                      title: '超用水量',
                      minWidth: 80,
                      slots: {
                        default: ({ row, rowIndex }) => {
                          return (
                            <div class={row.channelName == '小计' ? 'subtotalColor' : ''}>
                              {stageIndex == 0
                                ? row.waterRateList0?.overVolume
                                : stageIndex == 1
                                  ? row.waterRateList1?.overVolume
                                  : stageIndex == 2
                                    ? row.waterRateList2?.overVolume
                                    : ''}
                            </div>
                          )
                        },
                      },
                    },
                    {
                      title: '超20%内水量',
                      minWidth: 80,
                      slots: {
                        default: ({ row, rowIndex }) => {
                          return (
                            <div class={row.channelName == '小计' ? 'subtotalColor' : ''}>
                              {stageIndex == 0
                                ? row.waterRateList0?.overTwentyVolume
                                : stageIndex == 1
                                  ? row.waterRateList1?.overTwentyVolume
                                  : stageIndex == 2
                                    ? row.waterRateList2?.overTwentyVolume
                                    : ''}
                            </div>
                          )
                        },
                      },
                    },
                    {
                      title: '超20%-50%水量',
                      minWidth: 80,
                      slots: {
                        default: ({ row, rowIndex }) => {
                          return (
                            <div class={row.channelName == '小计' ? 'subtotalColor' : ''}>
                              {stageIndex == 0
                                ? row.waterRateList0?.overTwentyFiftyVolume
                                : stageIndex == 1
                                  ? row.waterRateList1?.overTwentyFiftyVolume
                                  : stageIndex == 2
                                    ? row.waterRateList2?.overTwentyFiftyVolume
                                    : ''}
                            </div>
                          )
                        },
                      },
                    },
                    {
                      title: '节水水量',
                      minWidth: 80,
                      slots: {
                        default: ({ row, rowIndex }) => {
                          return (
                            <div class={row.channelName == '小计' ? 'subtotalColor' : ''}>
                              {stageIndex == 0
                                ? row.waterRateList0?.savedVolume
                                : stageIndex == 1
                                  ? row.waterRateList1?.savedVolume
                                  : stageIndex == 2
                                    ? row.waterRateList2?.savedVolume
                                    : ''}
                            </div>
                          )
                        },
                      },
                    },
                  ],
                },
                {
                  title: '水费',
                  children: [
                    {
                      title: '指标内水费',
                      minWidth: 80,
                      slots: {
                        default: ({ row, rowIndex }) => {
                          return (
                            <div class={row.channelName == '小计' ? 'subtotalColor' : ''}>
                              {stageIndex == 0
                                ? row.waterRateList0?.allocatedFee.toFixed(2)
                                : stageIndex == 1
                                  ? row.waterRateList1?.allocatedFee.toFixed(2)
                                  : stageIndex == 2
                                    ? row.waterRateList2?.allocatedFee.toFixed(2)
                                    : ''}
                            </div>
                          )
                        },
                      },
                    },
                    {
                      title: '超水水费',
                      minWidth: 80,
                      slots: {
                        default: ({ row, rowIndex }) => {
                          return (
                            <div class={row.channelName == '小计' ? 'subtotalColor' : ''}>
                              {stageIndex == 0
                                ? row.waterRateList0?.overFee.toFixed(2)
                                : stageIndex == 1
                                  ? row.waterRateList1?.overFee.toFixed(2)
                                  : stageIndex == 2
                                    ? row.waterRateList2?.overFee.toFixed(2)
                                    : ''}
                            </div>
                          )
                        },
                      },
                    },
                    {
                      title: '水资源费',
                      minWidth: 80,
                      slots: {
                        default: ({ row, rowIndex }) => {
                          return (
                            <div class={row.channelName == '小计' ? 'subtotalColor' : ''}>
                              {stageIndex == 0
                                ? row.waterRateList0?.waterResourcesFee
                                : stageIndex == 1
                                  ? row.waterRateList1?.waterResourcesFee
                                  : stageIndex == 2
                                    ? row.waterRateList2?.waterResourcesFee
                                    : ''}
                            </div>
                          )
                        },
                      },
                    },
                    {
                      title: '合计水费',
                      minWidth: 90,
                      slots: {
                        default: ({ row, rowIndex }) => {
                          return (
                            <div class={row.channelName == '小计' ? 'subtotalColor' : ''}>
                              {stageIndex == 0
                                ? (
                                    (row.waterRateList0?.allocatedFee || 0) +
                                    (row.waterRateList0?.overFee || 0) +
                                    (row.waterRateList0?.waterResourcesFee || 0)
                                  ).toFixed(2)
                                : stageIndex == 1
                                  ? (
                                      (row.waterRateList1?.allocatedFee || 0) +
                                      (row.waterRateList1?.overFee || 0) +
                                      (row.waterRateList1?.waterResourcesFee || 0)
                                    ).toFixed(2)
                                  : stageIndex == 2
                                    ? (
                                        (row.waterRateList2?.allocatedFee || 0) +
                                        (row.waterRateList2?.overFee || 0) +
                                        (row.waterRateList2?.waterResourcesFee || 0)
                                      ).toFixed(2)
                                    : ''}
                            </div>
                          )
                        },
                      },
                    },
                  ],
                },
              ],
            }
          }),

          {
            field: '',
            title: '累计',
            children: [
              {
                title: '包干水量',
                minWidth: 80,
                slots: {
                  default: ({ row, rowIndex }) => {
                    return (
                      <div class={row.channelName == '小计' ? 'subtotalColor' : ''}>
                        {(
                          (row.waterRateList0?.contractedVolume || 0) +
                          (row.waterRateList1?.contractedVolume || 0) +
                          (row.waterRateList2?.contractedVolume || 0)
                        ).toFixed(2)}
                      </div>
                    )
                  },
                },
              },
              {
                title: '实用水量',
                minWidth: 80,
                slots: {
                  default: ({ row, rowIndex }) => {
                    return (
                      <div class={row.channelName == '小计' ? 'subtotalColor' : ''}>
                        {(
                          (row?.actualUsageDetailList0?.reduce((accumulator, currentValue) => {
                            return accumulator + currentValue?.actualUsage
                          }, 0) || 0) +
                          (row?.actualUsageDetailList1?.reduce((accumulator, currentValue) => {
                            return accumulator + currentValue?.actualUsage
                          }, 0) || 0) +
                          (row?.actualUsageDetailList2?.reduce((accumulator, currentValue) => {
                            return accumulator + currentValue?.actualUsage
                          }, 0) || 0)
                        ).toFixed(2)}
                      </div>
                    )
                  },
                },
              },
              {
                title: '水费',
                field: 'group60',
                children: [
                  {
                    title: '指标内水费',
                    minWidth: 80,
                    slots: {
                      default: ({ row, rowIndex }) => {
                        return (
                          <div class={row.channelName == '小计' ? 'subtotalColor' : ''}>
                            {(
                              (row.waterRateList0?.allocatedFee || 0) +
                              (row.waterRateList1?.allocatedFee || 0) +
                              (row.waterRateList2?.allocatedFee || 0)
                            ).toFixed(2)}
                          </div>
                        )
                      },
                    },
                  },
                  {
                    title: '超水水费',
                    minWidth: 80,
                    slots: {
                      default: ({ row, rowIndex }) => {
                        return (
                          <div class={row.channelName == '小计' ? 'subtotalColor' : ''}>
                            {(
                              (row.waterRateList0?.overFee || 0) +
                              (row.waterRateList1?.overFee || 0) +
                              (row.waterRateList2?.overFee || 0)
                            ).toFixed(2)}
                          </div>
                        )
                      },
                    },
                  },
                  {
                    title: '水资源费',
                    minWidth: 80,
                    slots: {
                      default: ({ row, rowIndex }) => {
                        return (
                          <div class={row.channelName == '小计' ? 'subtotalColor' : ''}>
                            {(
                              (row.waterRateList0?.waterResourcesFee || 0) +
                              (row.waterRateList1?.waterResourcesFee || 0) +
                              (row.waterRateList2?.waterResourcesFee || 0)
                            ).toFixed(2)}
                          </div>
                        )
                      },
                    },
                  },
                  {
                    title: '合计水费',
                    minWidth: 80,
                    slots: {
                      default: ({ row, rowIndex }) => {
                        return (
                          <div class={row.channelName == '小计' ? 'subtotalColor' : ''}>
                            {(
                              (row?.waterRateList0?.allocatedFee || 0) +
                              (row?.waterRateList1?.allocatedFee || 0) +
                              (row?.waterRateList2?.allocatedFee || 0) +
                              (row?.waterRateList0?.overFee || 0) +
                              (row?.waterRateList1?.overFee || 0) +
                              (row?.waterRateList2?.overFee || 0) +
                              (row?.waterRateList0?.waterResourcesFee || 0) +
                              (row?.waterRateList1?.waterResourcesFee || 0) +
                              (row?.waterRateList2?.waterResourcesFee || 0)
                            )?.toFixed(2)}
                          </div>
                        )
                      },
                    },
                  },
                ],
              },
            ],
          },
        ]
      },
      getTableData(detailVOList) {
        const arr1 = []
        const arr2 = []
        const arr3 = []
        detailVOList.forEach(el => {
          if (el.irrigationRound == 1) {
            arr1.push({
              ...el,
              channelDiff: el.channelNo + '-' + el.channelType + '-' + el.channelTypeSn,
              waterRateList0: el,
              actualUsageDetailList0: [...el.actualUsageDetailList],
            })
          } else if (el.irrigationRound == 2) {
            arr2.push({
              ...el,
              channelDiff: el.channelNo + '-' + el.channelType + '-' + el.channelTypeSn,
              waterRateList1: el,
              actualUsageDetailList1: [...el.actualUsageDetailList],
            })
          } else if (el.irrigationRound == 3) {
            arr3.push({
              ...el,
              channelDiff: el.channelNo + '-' + el.channelType + '-' + el.channelTypeSn,
              waterRateList2: el,
              actualUsageDetailList2: [...el.actualUsageDetailList],
            })
          }
        })

        function mergeAndDeduplicate(arrays) {
          const mergedMap = new Map()
          const mergedArray = arrays.flat()
          mergedArray.forEach(item => {
            const key = item.channelDiff

            if (mergedMap.has(key)) {
              const existingItem = mergedMap.get(key)
              Object.keys(item).forEach(prop => {
                if (Array.isArray(item[prop])) {
                  if (!existingItem[prop]) {
                    existingItem[prop] = item[prop]
                  } else {
                    // 如果已存在，可以选择合并或跳过
                    // existingItem[prop] = [...existingItem[prop], ...item[prop]];
                  }
                } else if (prop !== 'channelDiff') {
                  existingItem[prop] = item[prop]
                }
              })
            } else {
              mergedMap.set(key, { ...item })
            }
          })

          return Array.from(mergedMap.values())
        }

        const tableList = mergeAndDeduplicate([arr1, arr2, arr3])
        this.tableData = this.transformDataWithSubtotal(tableList)
        this.getTableColumns()
      },
      transformDataWithSubtotal(data) {
        const result = []
        let currentChannelType = null
        let currentChannelTypeSn = null

        // 小计对象
        let subtotal = {
          irrArea: 0,
          conversionRate: 0,
          feeLeTwenty: 0,
          feeGeTwenty: 0,
          waterRate0: {
            contractedVolume: 0,
            allocatedVolume: 0,
            overVolume: 0,
            overTwentyVolume: 0,
            overTwentyFiftyVolume: 0,
            savedVolume: 0,
            allocatedFee: 0,
            overFee: 0,
            waterResourcesFee: 0,
          },
          waterRate1: {
            contractedVolume: 0,
            allocatedVolume: 0,
            overVolume: 0,
            overTwentyVolume: 0,
            overTwentyFiftyVolume: 0,
            savedVolume: 0,
            allocatedFee: 0,
            overFee: 0,
            waterResourcesFee: 0,
          },
          waterRate2: {
            contractedVolume: 0,
            allocatedVolume: 0,
            overVolume: 0,
            overTwentyVolume: 0,
            overTwentyFiftyVolume: 0,
            savedVolume: 0,
            allocatedFee: 0,
            overFee: 0,
            waterResourcesFee: 0,
          },
          usageByMonth0: {},
          usageByMonth1: {},
          usageByMonth2: {},
          count: 0,
        }

        // 总计对象
        let grandTotal = {
          irrArea: 0,
          conversionRate: 0,
          feeLeTwenty: 0,
          feeGeTwenty: 0,
          waterRate0: {
            contractedVolume: 0,
            allocatedVolume: 0,
            overVolume: 0,
            overTwentyVolume: 0,
            overTwentyFiftyVolume: 0,
            savedVolume: 0,
            allocatedFee: 0,
            overFee: 0,
            waterResourcesFee: 0,
          },
          waterRate1: {
            contractedVolume: 0,
            allocatedVolume: 0,
            overVolume: 0,
            overTwentyVolume: 0,
            overTwentyFiftyVolume: 0,
            savedVolume: 0,
            allocatedFee: 0,
            overFee: 0,
            waterResourcesFee: 0,
          },
          waterRate2: {
            contractedVolume: 0,
            allocatedVolume: 0,
            overVolume: 0,
            overTwentyVolume: 0,
            overTwentyFiftyVolume: 0,
            savedVolume: 0,
            allocatedFee: 0,
            overFee: 0,
            waterResourcesFee: 0,
          },
          usageByMonth0: {},
          usageByMonth1: {},
          usageByMonth2: {},
          count: 0,
        }

        // 先按 channelType 和 channelTypeSn 排序
        const sortedData = [...data].sort((a, b) => {
          if (a.channelType === b.channelType) {
            return a.channelTypeSn - b.channelTypeSn
          }
          return a.channelType - b.channelType
        })

        for (const item of sortedData) {
          if (
            currentChannelType !== null &&
            (currentChannelType !== item.channelType || currentChannelTypeSn !== item.channelTypeSn)
          ) {
            this.addSubtotalRow(result, subtotal)

            // 重置小计
            subtotal = {
              irrArea: 0,
              conversionRate: 0,
              feeLeTwenty: 0,
              feeGeTwenty: 0,
              waterRate0: {
                contractedVolume: 0,
                allocatedVolume: 0,
                overVolume: 0,
                overTwentyVolume: 0,
                overTwentyFiftyVolume: 0,
                savedVolume: 0,
                allocatedFee: 0,
                overFee: 0,
                waterResourcesFee: 0,
              },
              waterRate1: {
                contractedVolume: 0,
                allocatedVolume: 0,
                overVolume: 0,
                overTwentyVolume: 0,
                overTwentyFiftyVolume: 0,
                savedVolume: 0,
                allocatedFee: 0,
                overFee: 0,
                waterResourcesFee: 0,
              },
              waterRate2: {
                contractedVolume: 0,
                allocatedVolume: 0,
                overVolume: 0,
                overTwentyVolume: 0,
                overTwentyFiftyVolume: 0,
                savedVolume: 0,
                allocatedFee: 0,
                overFee: 0,
                waterResourcesFee: 0,
              },
              usageByMonth0: {},
              usageByMonth1: {},
              usageByMonth2: {},
              count: 0,
            }
          }

          // 添加当前项目
          result.push(item)

          // 更新小计和总计
          currentChannelType = item.channelType
          currentChannelTypeSn = item.channelTypeSn

          // 更新费用数据
          subtotal.irrArea += item.irrArea
          subtotal.conversionRate += item.conversionRate
          subtotal.feeLeTwenty += item.feeLeTwenty
          subtotal.feeGeTwenty += item.feeGeTwenty
          subtotal.count++

          grandTotal.irrArea += item.irrArea
          grandTotal.conversionRate += item.conversionRate
          grandTotal.feeLeTwenty += item.feeLeTwenty
          grandTotal.feeGeTwenty += item.feeGeTwenty
          grandTotal.count++

          // 更新水量数据0
          if (item.waterRateList0) {
            subtotal.waterRate0.contractedVolume += item.waterRateList0.contractedVolume
            subtotal.waterRate0.allocatedVolume += item.waterRateList0.allocatedVolume
            subtotal.waterRate0.overVolume += item.waterRateList0.overVolume
            subtotal.waterRate0.overTwentyVolume += item.waterRateList0.overTwentyVolume
            subtotal.waterRate0.overTwentyFiftyVolume += item.waterRateList0.overTwentyFiftyVolume
            subtotal.waterRate0.savedVolume += item.waterRateList0.savedVolume
            subtotal.waterRate0.allocatedFee += item.waterRateList0.allocatedFee
            subtotal.waterRate0.overFee += item.waterRateList0.overFee
            subtotal.waterRate0.waterResourcesFee += item.waterRateList0.waterResourcesFee

            grandTotal.waterRate0.contractedVolume += item.waterRateList0.contractedVolume
            grandTotal.waterRate0.allocatedVolume += item.waterRateList0.allocatedVolume
            grandTotal.waterRate0.overVolume += item.waterRateList0.overVolume
            grandTotal.waterRate0.overTwentyVolume += item.waterRateList0.overTwentyVolume
            grandTotal.waterRate0.overTwentyFiftyVolume += item.waterRateList0.overTwentyFiftyVolume
            grandTotal.waterRate0.savedVolume += item.waterRateList0.savedVolume
            grandTotal.waterRate0.allocatedFee += item.waterRateList0.allocatedFee
            grandTotal.waterRate0.overFee += item.waterRateList0.overFee
            grandTotal.waterRate0.waterResourcesFee += item.waterRateList0.waterResourcesFee
          }

          // 更新水量数据1
          if (item.waterRateList1) {
            subtotal.waterRate1.contractedVolume += item.waterRateList1.contractedVolume
            subtotal.waterRate1.allocatedVolume += item.waterRateList1.allocatedVolume
            subtotal.waterRate1.overVolume += item.waterRateList1.overVolume
            subtotal.waterRate1.overTwentyVolume += item.waterRateList1.overTwentyVolume
            subtotal.waterRate1.overTwentyFiftyVolume += item.waterRateList1.overTwentyFiftyVolume
            subtotal.waterRate1.savedVolume += item.waterRateList1.savedVolume
            subtotal.waterRate1.allocatedFee += item.waterRateList1.allocatedFee
            subtotal.waterRate1.overFee += item.waterRateList1.overFee
            subtotal.waterRate1.waterResourcesFee += item.waterRateList1.waterResourcesFee

            grandTotal.waterRate1.contractedVolume += item.waterRateList1.contractedVolume
            grandTotal.waterRate1.allocatedVolume += item.waterRateList1.allocatedVolume
            grandTotal.waterRate1.overVolume += item.waterRateList1.overVolume
            grandTotal.waterRate1.overTwentyVolume += item.waterRateList1.overTwentyVolume
            grandTotal.waterRate1.overTwentyFiftyVolume += item.waterRateList1.overTwentyFiftyVolume
            grandTotal.waterRate1.savedVolume += item.waterRateList1.savedVolume
            grandTotal.waterRate1.allocatedFee += item.waterRateList1.allocatedFee
            grandTotal.waterRate1.overFee += item.waterRateList1.overFee
            grandTotal.waterRate1.waterResourcesFee += item.waterRateList1.waterResourcesFee
          }

          if (item.waterRateList2) {
            subtotal.waterRate2.contractedVolume += item.waterRateList2.contractedVolume
            subtotal.waterRate2.allocatedVolume += item.waterRateList2.allocatedVolume
            subtotal.waterRate2.overVolume += item.waterRateList2.overVolume
            subtotal.waterRate2.overTwentyVolume += item.waterRateList2.overTwentyVolume
            subtotal.waterRate2.overTwentyFiftyVolume += item.waterRateList2.overTwentyFiftyVolume
            subtotal.waterRate2.savedVolume += item.waterRateList2.savedVolume
            subtotal.waterRate2.allocatedFee += item.waterRateList2.allocatedFee
            subtotal.waterRate2.overFee += item.waterRateList2.overFee
            subtotal.waterRate2.waterResourcesFee += item.waterRateList2.waterResourcesFee

            grandTotal.waterRate2.contractedVolume += item.waterRateList2.contractedVolume
            grandTotal.waterRate2.allocatedVolume += item.waterRateList2.allocatedVolume
            grandTotal.waterRate2.overVolume += item.waterRateList2.overVolume
            grandTotal.waterRate2.overTwentyVolume += item.waterRateList2.overTwentyVolume
            grandTotal.waterRate2.overTwentyFiftyVolume += item.waterRateList2.overTwentyFiftyVolume
            grandTotal.waterRate2.savedVolume += item.waterRateList2.savedVolume
            grandTotal.waterRate2.allocatedFee += item.waterRateList2.allocatedFee
            grandTotal.waterRate2.overFee += item.waterRateList2.overFee
            grandTotal.waterRate2.waterResourcesFee += item.waterRateList2.waterResourcesFee
          }

          // 汇总实际使用量
          if (item.actualUsageDetailList0) {
            item.actualUsageDetailList0.forEach(usage => {
              const month = usage.useMonth
              subtotal.usageByMonth0[month] = (subtotal.usageByMonth0[month] || 0) + usage.actualUsage
              grandTotal.usageByMonth0[month] = (grandTotal.usageByMonth0[month] || 0) + usage.actualUsage
            })
          }
          if (item.actualUsageDetailList1) {
            item.actualUsageDetailList1?.forEach(usage => {
              const month = usage.useMonth
              subtotal.usageByMonth1[month] = (subtotal.usageByMonth1[month] || 0) + usage.actualUsage
              grandTotal.usageByMonth1[month] = (grandTotal.usageByMonth1[month] || 0) + usage.actualUsage
            })
          }
          if (item.actualUsageDetailList2) {
            item.actualUsageDetailList2?.forEach(usage => {
              const month = usage.useMonth
              subtotal.usageByMonth2[month] = (subtotal.usageByMonth2[month] || 0) + usage.actualUsage
              grandTotal.usageByMonth2[month] = (grandTotal.usageByMonth2[month] || 0) + usage.actualUsage
            })
          }
        }

        if (currentChannelType !== null && subtotal.count > 0) {
          this.addSubtotalRow(result, subtotal)
        }
        // 添加总计行
        this.addGrandTotalRow(result, grandTotal)

        return result
      },

      // 添加小计行
      addSubtotalRow(result, subtotal) {
        const subtotalUsageList0 = Object.entries(subtotal.usageByMonth0)
          .map(([month, usage]) => ({
            actualUsage: usage,
            useMonth: parseInt(month),
          }))
          .sort((a, b) => a.useMonth - b.useMonth)

        const subtotalUsageList1 = Object.entries(subtotal.usageByMonth1)
          .map(([month, usage]) => ({
            actualUsage: usage,
            useMonth: parseInt(month),
          }))
          .sort((a, b) => a.useMonth - b.useMonth)

        const subtotalUsageList2 = Object.entries(subtotal.usageByMonth2)
          .map(([month, usage]) => ({
            actualUsage: usage,
            useMonth: parseInt(month),
          }))
          .sort((a, b) => a.useMonth - b.useMonth)

        result.push({
          channelNo: '-1',
          channelName: '小计',
          type: '支',
          channelType: '',
          channelTypeSn: '',
          irrArea: subtotal.irrArea != 0.0 ? subtotal.irrArea.toFixed(2) : '',
          conversionRate: subtotal.conversionRate.toFixed(2),
          feeLeTwenty: subtotal.feeLeTwenty.toFixed(2),
          feeGeTwenty: subtotal.feeGeTwenty.toFixed(2),
          waterRateList0: {
            contractedVolume: subtotal.waterRate0.contractedVolume,
            allocatedVolume: subtotal.waterRate0.allocatedVolume,
            overVolume: subtotal.waterRate0.overVolume,
            overTwentyVolume: subtotal.waterRate0.overTwentyVolume.toFixed(2),
            overTwentyFiftyVolume: subtotal.waterRate0.overTwentyFiftyVolume.toFixed(2),
            savedVolume: subtotal.waterRate0.savedVolume,
            allocatedFee: subtotal.waterRate0.allocatedFee,
            overFee: subtotal.waterRate0.overFee,
            waterResourcesFee: subtotal.waterRate0.waterResourcesFee,
          },
          waterRateList1: {
            contractedVolume: subtotal.waterRate1.contractedVolume,
            allocatedVolume: subtotal.waterRate1.allocatedVolume,
            overVolume: subtotal.waterRate1.overVolume,
            overTwentyVolume: subtotal.waterRate1.overTwentyVolume.toFixed(2),
            overTwentyFiftyVolume: subtotal.waterRate1.overTwentyFiftyVolume.toFixed(2),
            savedVolume: subtotal.waterRate1.savedVolume,
            allocatedFee: subtotal.waterRate1.allocatedFee,
            overFee: subtotal.waterRate1.overFee,
            waterResourcesFee: subtotal.waterRate1.waterResourcesFee,
          },
          waterRateList2: {
            contractedVolume: subtotal.waterRate2.contractedVolume,
            allocatedVolume: subtotal.waterRate2.allocatedVolume,
            overVolume: subtotal.waterRate2.overVolume,
            overTwentyVolume: subtotal.waterRate2.overTwentyVolume.toFixed(2),
            overTwentyFiftyVolume: subtotal.waterRate2.overTwentyFiftyVolume.toFixed(2),
            savedVolume: subtotal.waterRate2.savedVolume,
            allocatedFee: subtotal.waterRate2.allocatedFee,
            overFee: subtotal.waterRate2.overFee,
            waterResourcesFee: subtotal.waterRate2.waterResourcesFee,
          },
          actualUsageDetailList0: subtotalUsageList0,
          actualUsageDetailList1: subtotalUsageList1,
          actualUsageDetailList2: subtotalUsageList2,
          count: subtotal.count,
        })
      },

      // 添加总计行
      addGrandTotalRow(result, grandTotal) {
        const totalUsageList0 = Object.entries(grandTotal.usageByMonth0)
          .map(([month, usage]) => ({
            actualUsage: usage,
            useMonth: parseInt(month),
          }))
          .sort((a, b) => a.useMonth - b.useMonth)

        const totalUsageList1 = Object.entries(grandTotal.usageByMonth1)
          .map(([month, usage]) => ({
            actualUsage: usage,
            useMonth: parseInt(month),
          }))
          .sort((a, b) => a.useMonth - b.useMonth)

        const totalUsageList2 = Object.entries(grandTotal.usageByMonth2)
          .map(([month, usage]) => ({
            actualUsage: usage,
            useMonth: parseInt(month),
          }))
          .sort((a, b) => a.useMonth - b.useMonth)

        result.push({
          channelNo: '-1',
          channelName: '合计',
          type: '支',
          channelType: '',
          channelTypeSn: '',
          irrArea: grandTotal.irrArea != 0.0 ? grandTotal.irrArea.toFixed(2) : '',
          conversionRate: grandTotal.conversionRate.toFixed(2),
          feeLeTwenty: grandTotal.feeLeTwenty.toFixed(2),
          feeGeTwenty: grandTotal.feeGeTwenty.toFixed(2),
          waterRateList0: {
            contractedVolume: grandTotal.waterRate0.contractedVolume,
            allocatedVolume: grandTotal.waterRate0.allocatedVolume,
            overVolume: grandTotal.waterRate0.overVolume,
            overTwentyVolume: grandTotal.waterRate0.overTwentyVolume.toFixed(2),
            overTwentyFiftyVolume: grandTotal.waterRate0.overTwentyFiftyVolume.toFixed(2),
            savedVolume: grandTotal.waterRate0.savedVolume,
            allocatedFee: grandTotal.waterRate0.allocatedFee,
            overFee: grandTotal.waterRate0.overFee,
            waterResourcesFee: grandTotal.waterRate0.waterResourcesFee,
          },
          waterRateList1: {
            contractedVolume: grandTotal.waterRate1.contractedVolume,
            allocatedVolume: grandTotal.waterRate1.allocatedVolume,
            overVolume: grandTotal.waterRate1.overVolume,
            overTwentyVolume: grandTotal.waterRate1.overTwentyVolume.toFixed(2),
            overTwentyFiftyVolume: grandTotal.waterRate1.overTwentyFiftyVolume.toFixed(2),
            savedVolume: grandTotal.waterRate1.savedVolume,
            allocatedFee: grandTotal.waterRate1.allocatedFee,
            overFee: grandTotal.waterRate1.overFee,
            waterResourcesFee: grandTotal.waterRate1.waterResourcesFee,
          },
          waterRateList2: {
            contractedVolume: grandTotal.waterRate2.contractedVolume,
            allocatedVolume: grandTotal.waterRate2.allocatedVolume,
            overVolume: grandTotal.waterRate2.overVolume,
            overTwentyVolume: grandTotal.waterRate2.overTwentyVolume.toFixed(2),
            overTwentyFiftyVolume: grandTotal.waterRate2.overTwentyFiftyVolume.toFixed(2),
            savedVolume: grandTotal.waterRate2.savedVolume,
            allocatedFee: grandTotal.waterRate2.allocatedFee,
            overFee: grandTotal.waterRate2.overFee,
            waterResourcesFee: grandTotal.waterRate2.waterResourcesFee,
          },
          actualUsageDetailList0: totalUsageList0,
          actualUsageDetailList1: totalUsageList1,
          actualUsageDetailList2: totalUsageList2,
          count: grandTotal.count,
        })
      },
      /** 搜索按钮操作 */
      handleQuery() {
        this.getList()
      },
      /** 重置按钮操作 */
      resetQuery() {
        this.yearValue = moment()
        ;((this.queryParam = {
          depId: this.deptOptions[0]?.deptId,
          fillYear: moment().year(),
        }),
          this.handleQuery())
      },

      handleImport() {},
      handleExport() {},
    },
  }
</script>
<style lang="less" scoped>
  .common-table-page {
    background: #fff;
    .flex,
    .ant-form-item {
      display: flex;
    }
    .row-pad {
      padding: 0 15px;
    }

    .table-operations {
      .ant-btn {
        &:last-child {
          margin-right: 0px;
        }
      }
    }
    .vxe-table-content .vxe-table-box {
      height: 98% !important;
    }
    ::v-deep(.header-bar) {
      .title {
        text-align: center;
        width: 100%;
        position: absolute;
      }
    }
    ::v-deep(.vxe-table--header) {
      .first-col {
        position: relative;
        height: 20px;
        &:before {
          content: '';
          position: absolute;
          left: -68px;
          top: 6px;
          width: 230px;
          height: 1px;
          transform: rotate(55deg);
          background-color: #e8eaec;
        }
        .first-col-top {
          position: absolute;
          right: 4px;
          top: -50px;
        }
        .first-col-bottom {
          position: absolute;
          left: 4px;
          bottom: -38px;
        }
      }
    }
    ::v-deep(.subtotalColor) {
      color: #1890ff;
    }
  }
</style>
